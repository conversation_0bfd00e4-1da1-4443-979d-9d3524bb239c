import { Button } from '@/components/ui/button';
import { durations, easings } from '@/utils/animations';
import { AnimatePresence, motion } from 'framer-motion';
import { Menu, X } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

export interface NavigationItem {
  label: string;
  sectionId: string;
}

interface NavigationBarProps {
  navItems: NavigationItem[];
  onNavigate?: (sectionId: string) => void;
  className?: string;
}

export const NavigationBar: React.FC<NavigationBarProps> = ({
  navItems,
  onNavigate,
  className = '',
}) => {
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [showFlagDropdown, setShowFlagDropdown] = useState(false);
  const [selectedFlag, setSelectedFlag] = useState(0); // US is default (index 0)
  const mobileMenuRef = useRef<HTMLDivElement>(null);
  const mobileMenuButtonRef = useRef<HTMLButtonElement>(null);
  const flagDropdownRef = useRef<HTMLDivElement>(null);

  // Flag data
  const flags = [
    { name: 'United Kingdom', code: 'UK', flag: '🇬🇧' },
    { name: 'United States', code: 'US', flag: '🇺🇸' },
    { name: 'India', code: 'IN', flag: '🇮🇳' },
    { name: 'European Union', code: 'EU', flag: '🇪🇺' },
  ];

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;

      // Check if click is outside mobile menu and not on the menu button
      if (mobileMenuRef.current && !mobileMenuRef.current.contains(target)) {
        // Also check if the click is not on the mobile menu button itself
        if (!mobileMenuButtonRef.current || !mobileMenuButtonRef.current.contains(target)) {
          setShowMobileMenu(false);
        }
      }

      // Check if click is outside flag dropdown
      if (flagDropdownRef.current && !flagDropdownRef.current.contains(target)) {
        setShowFlagDropdown(false);
      }
    };

    if (showMobileMenu || showFlagDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showMobileMenu, showFlagDropdown]);

  const handleMobileMenuClick = () => {
    setShowMobileMenu(!showMobileMenu);
  };

  // Handle flag selection with navigation
  const handleFlagSelection = (index: number) => {
    setSelectedFlag(index);
    setShowFlagDropdown(false);

    // Navigate to India site if IN flag is selected
    if (flags[index].code === 'IN') {
      window.location.href = 'https://redgirraffe.com/in/';
    }
  };

  // Enhanced smooth scroll to section function
  const scrollToSection = (sectionId: string, closeMobileMenu: boolean = false) => {
    const element = document.getElementById(sectionId);
    if (element) {
      // Account for sticky navigation height
      const headerOffset = 80;

      const performScroll = () => {
        const elementPosition = element.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

        // Enhanced smooth scrolling with optimized performance
        const startPosition = window.pageYOffset;
        const distance = offsetPosition - startPosition;
        // Optimized duration: faster base speed with reasonable max duration
        const duration = Math.min(Math.abs(distance) * 0.2 + 200, 800); // Much faster scrolling
        let start: number | null = null;

        const easeInOutCubic = (t: number): number => {
          return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
        };

        const animateScroll = (timestamp: number) => {
          if (start === null) start = timestamp;
          const progress = Math.min((timestamp - start) / duration, 1);
          const ease = easeInOutCubic(progress);

          window.scrollTo(0, startPosition + distance * ease);

          if (progress < 1) {
            requestAnimationFrame(animateScroll);
          }
        };

        requestAnimationFrame(animateScroll);
      };

      if (closeMobileMenu && showMobileMenu) {
        // Close menu first, then scroll after animation completes
        setShowMobileMenu(false);
        setTimeout(performScroll, 350); // Wait for menu close animation
      } else {
        performScroll();
      }
    }

    // Call optional callback
    if (onNavigate) {
      onNavigate(sectionId);
    }
  };

  return (
    <>
      {/* Navigation Bar - Sticky with Glass Morphism Effect */}
      <div className={`sticky top-0 z-50 flex items-center justify-center w-full ${className}`}>
        {/* Glass Morphism Background - Stripe-like transparent overlay */}
        <div className="absolute inset-0 bg-white/20 backdrop-blur-lg "></div>
        <div className="max-w-none px-1 sm:px-2 lg:px-3 xl:px-4 relative z-10 w-full">
          <div className="flex h-16 sm:h-16 lg:h-20 items-center justify-between w-full max-w-[98%] xl:max-w-[96%] mx-auto py-4 sm:py-4 lg:py-6">
            <div className="flex items-center gap-3 lg:gap-8 xl:gap-12">
              {/* Logo - Enhanced for premium synergy */}
              <div className="flex items-center gap-2 sm:gap-3">
                <div className="font-bold text-slate-900 text-2xl sm:text-2xl lg:text-3xl xl:text-3xl tracking-tight bg-gradient-to-r from-slate-900 via-slate-800 to-slate-900 bg-clip-text text-transparent hover:from-emerald-700 hover:via-slate-900 hover:to-emerald-700 transition-all duration-300 drop-shadow-sm">
                  RedGirraffe
                </div>
              </div>

              {/* Navigation Links - Hidden on mobile and tablet */}
              <div className="hidden lg:flex items-center gap-4 xl:gap-6">
                {navItems.map((item, index) => (
                  <div
                    key={index}
                    className="p-0 h-auto hover:bg-transparent btn-touch"
                    onClick={() => scrollToSection(item.sectionId)}
                  >
                    <div className="font-body-large-body-large-semibold text-sm lg:text-base xl:text-[length:var(--body-large-body-large-semibold-font-size)] text-center tracking-[var(--body-large-body-large-semibold-letter-spacing)] leading-[var(--body-large-body-large-semibold-line-height)] transition-colors cursor-pointer text-slate-800 hover:text-emerald-600 font-medium drop-shadow-sm">
                      {item.label}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-center gap-1 xl:gap-2">
              {/* Mobile Menu Button - Show only on mobile */}
              <Button
                ref={mobileMenuButtonRef}
                variant="ghost"
                className="block lg:hidden w-10 h-10 p-2 btn-touch group"
                onClick={handleMobileMenuClick}
              >
                <motion.div
                  initial={false}
                  animate={{ rotate: showMobileMenu ? 90 : 0 }}
                  transition={{ duration: durations.fast, ease: easings.smooth }}
                >
                  {showMobileMenu ? (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: durations.fast }}
                    >
                      <X className="w-6 h-6 text-slate-800 group-hover:text-emerald-600" />
                    </motion.div>
                  ) : (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: durations.fast }}
                    >
                      <Menu className="w-6 h-6 text-slate-800 group-hover:text-emerald-600" />
                    </motion.div>
                  )}
                </motion.div>
              </Button>

              {/* Desktop Buttons - Hidden on mobile */}
              <div className="hidden lg:flex items-center gap-1 xl:gap-2">
                {/* Flag Dropdown */}
                <div className="relative" ref={flagDropdownRef}>
                  <Button
                    variant="ghost"
                    className="w-12 h-12 p-0 rounded-full hover:bg-gray-300 transition-colors btn-touch flex items-center justify-center"
                    onClick={() => setShowFlagDropdown(!showFlagDropdown)}
                  >
                    <span className="text-3xl leading-none">{flags[selectedFlag].flag}</span>
                  </Button>

                  {/* Flag Dropdown Menu */}
                  <AnimatePresence>
                    {showFlagDropdown && (
                      <motion.div
                        className="absolute top-12 right-0 bg-white border border-abu-stroke rounded-lg shadow-lg py-2 min-w-[160px] z-50"
                        initial={{ opacity: 0, y: -10, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: -10, scale: 0.95 }}
                        transition={{ duration: durations.fast }}
                      >
                        {flags.map((flag, index) => (
                          <motion.button
                            key={index}
                            onClick={() => handleFlagSelection(index)}
                            className={`w-full flex items-center gap-3 px-4 py-2 text-left hover:bg-gray-50 transition-colors ${
                              index === selectedFlag ? 'bg-gray-50' : ''
                            }`}
                            whileHover={{ backgroundColor: '#f9fafb' }}
                          >
                            <span className="text-xl">{flag.flag}</span>
                            <span className="font-body-medium-body-medium-regular text-black text-sm">
                              {flag.code}
                            </span>
                          </motion.button>
                        ))}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                <Button
                  variant="outline"
                  className="bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 border-0 text-white font-semibold px-6 py-2.5 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                >
                  <span className="text-sm font-medium">Get Demo</span>
                </Button>
                <Button className="bg-white/20 hover:bg-white/30 backdrop-blur-sm border border-white/40 text-slate-800 hover:text-slate-900 font-semibold px-6 py-2.5 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                  <span className="text-sm font-medium">Login</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      <AnimatePresence>
        {showMobileMenu && (
          <motion.div
            ref={mobileMenuRef}
            className="lg:hidden w-full bg-white/90 backdrop-blur-lg border-b border-white/30 px-4 py-4 sticky top-[80px] z-40 shadow-lg"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: durations.fast }}
          >
            <div className="flex flex-col gap-4">
              {navItems.map((item, index) => (
                <motion.button
                  key={index}
                  onClick={() => scrollToSection(item.sectionId, true)}
                  className="text-left py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors btn-touch"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{
                    duration: durations.fast,
                    delay: index * 0.1,
                  }}
                >
                  <span className="font-body-large-body-large-semibold text-textblack text-base">
                    {item.label}
                  </span>
                </motion.button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};
