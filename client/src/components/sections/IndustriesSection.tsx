import { Button } from '@/components/ui/button';
import {
  ArrowRight,
  Briefcase,
  Building2,
  Factory,
  GraduationCap,
  Heart,
  Hotel,
  Settings,
  Store,
  TrendingUp,
  Zap,
} from 'lucide-react';

export const IndustriesSection = () => {
  const industries = [
    {
      icon: Building2,
      title: 'Data Centres',
      description: 'Optimize infrastructure payments.',
      color: 'bg-slate-50 hover:bg-slate-100',
      iconColor: 'text-green-600',
    },
    {
      icon: TrendingUp,
      title: 'Logistics',
      description: 'Fund shipping and contractor payments.',
      color: 'bg-slate-50 hover:bg-slate-100',
      iconColor: 'text-green-600',
    },
    {
      icon: Factory,
      title: 'Manufacturing',
      description: 'Support supply chain payments.',
      color: 'bg-slate-50 hover:bg-slate-100',
      iconColor: 'text-green-600',
    },
    {
      icon: Settings,
      title: 'Technology',
      description: 'Streamline hardware payments.',
      color: 'bg-slate-50 hover:bg-slate-100',
      iconColor: 'text-green-600',
    },
    {
      icon: Building2,
      title: 'Construction',
      description: 'Fund materials and contractors.',
      color: 'bg-slate-50 hover:bg-slate-100',
      iconColor: 'text-green-600',
    },
    {
      icon: Briefcase,
      title: 'SaaS Companies',
      description: 'Automate subscription payments.',
      color: 'bg-slate-50 hover:bg-slate-100',
      iconColor: 'text-green-600',
    },
    {
      icon: TrendingUp,
      title: 'Insurance',
      description: 'Optimize infrastructure payments.',
      color: 'bg-slate-50 hover:bg-slate-100',
      iconColor: 'text-green-600',
    },
    {
      icon: Heart,
      title: 'Healthcare',
      description: 'Settle equipment payments securely.',
      color: 'bg-slate-50 hover:bg-slate-100',
      iconColor: 'text-green-600',
    },
    {
      icon: Hotel,
      title: 'Hospitality',
      description: 'Manage hotel supplier payments.',
      color: 'bg-slate-50 hover:bg-slate-100',
      iconColor: 'text-green-600',
    },
    {
      icon: GraduationCap,
      title: 'Education',
      description: 'Support facility payments.',
      color: 'bg-slate-50 hover:bg-slate-100',
      iconColor: 'text-green-600',
    },
    {
      icon: Briefcase,
      title: 'Media & Entertainment',
      description: 'Settle event payments.',
      color: 'bg-slate-50 hover:bg-slate-100',
      iconColor: 'text-green-600',
    },
    {
      icon: Briefcase,
      title: 'Professional Services',
      description: 'Automate consulting fees.',
      color: 'bg-slate-50 hover:bg-slate-100',
      iconColor: 'text-green-600',
    },
    {
      icon: Store,
      title: 'Real Estate Funds',
      description: 'Simplify property payments.',
      color: 'bg-slate-50 hover:bg-slate-100',
      iconColor: 'text-green-600',
    },
    {
      icon: Store,
      title: 'Retail Chains',
      description: 'Streamline multi-location vendor payments.',
      color: 'bg-slate-50 hover:bg-slate-100',
      iconColor: 'text-green-600',
    },
    {
      icon: Zap,
      title: 'Energy & Utilities',
      description: 'Cover infrastructure payments.',
      color: 'bg-slate-50 hover:bg-slate-100',
      iconColor: 'text-green-600',
    },
  ];

  return (
    <section className="section-spacing-tight bg-gradient-to-br from-amber-50/30 via-yellow-50/20 to-lime-50/30 relative overflow-hidden">
      {/* Minimal Background Pattern */}
      <div className="absolute inset-0 opacity-[0.015]">
        <div className="absolute top-20 left-20 w-96 h-96 bg-green-200 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-72 h-72 bg-emerald-200 rounded-full blur-3xl"></div>
      </div>

      {/* Synchronized with Hero section margins for perfect consistency */}
      <div className="max-w-none px-1 sm:px-2 lg:px-3 xl:px-4 w-full">
        <div className="w-full max-w-[98%] xl:max-w-[96%] mx-auto relative">
          {/* Header */}
          <div className="text-center mb-12 sm:mb-16 md:mb-20">
            <h2 className="text-responsive-xl font-bold text-slate-900 mb-4 sm:mb-6 tracking-tight">
              Industries{' '}
              <span className="bg-gradient-to-r from-violet-600 via-purple-500 to-indigo-600 bg-clip-text text-transparent">
                we serve
              </span>
            </h2>
            <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed">
              Tailored payment solutions for diverse business sectors with enterprise-grade security
              and global reach.
            </p>
          </div>

          {/* Industries Grid */}
          <div className="grid-responsive-2-4 gap-responsive-sm mb-12 sm:mb-16">
            {industries.map((industry, index) => (
              <div
                key={index}
                className="bg-white padding-responsive-sm rounded-2xl sm:rounded-3xl transition-all duration-500 group cursor-pointer border border-slate-100 hover:shadow-2xl hover:border-emerald-200 transform hover:scale-105 hover:-translate-y-2"
              >
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                    <industry.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="font-bold text-slate-900 mb-3 text-lg leading-tight group-hover:text-emerald-600 transition-colors duration-300">
                    {industry.title}
                  </h3>
                  <p className="text-sm text-slate-600 leading-relaxed group-hover:text-slate-700 transition-colors duration-300">
                    {industry.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Call to Action */}
          <div className="text-center">
            <Button className="bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-500 hover:to-green-500 text-white rounded-full px-12 py-6 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-3 mx-auto group">
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
              Request a Demo
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};
