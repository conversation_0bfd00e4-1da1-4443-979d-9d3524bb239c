import { Button } from '@/components/ui/button';
import { ArrowRight, CheckCircle, Globe, Shield, TrendingUp, Users, Zap } from 'lucide-react';

export const MainContentSection = () => {
  const features = [
    {
      icon: Shield,
      title: 'Bank-Grade Security',
      description:
        'Enterprise-level encryption and compliance standards protect every transaction with military-grade security protocols.',
    },
    {
      icon: Zap,
      title: 'Lightning-Fast Processing',
      description:
        'Process payments in milliseconds with our advanced infrastructure, ensuring seamless customer experiences.',
    },
    {
      icon: Globe,
      title: 'Global Market Reach',
      description:
        'Accept payments in 180+ countries with local payment methods and multi-currency support.',
    },
    {
      icon: TrendingUp,
      title: 'Revenue Optimization',
      description:
        'Intelligent routing and dynamic pricing strategies maximize your revenue while minimizing processing costs.',
    },
    {
      icon: Users,
      title: 'Customer-First Design',
      description:
        'Intuitive interfaces and seamless checkout experiences drive higher conversion rates and customer satisfaction.',
    },
    {
      icon: CheckCircle,
      title: 'Compliance Assurance',
      description:
        'Stay compliant with PCI DSS, GDPR, and regional regulations through automated compliance management.',
    },
  ];

  return (
    <section className="bg-gradient-to-br from-slate-50 via-white to-green-50/30 py-20 lg:py-32">
      <div className="max-w-none px-1 sm:px-2 lg:px-3 xl:px-4 w-full">
        <div className="max-w-[98%] xl:max-w-[96%] mx-auto">
          <div className="text-center mb-20">
            <h2 className="text-responsive-xl font-bold text-slate-900 mb-6 tracking-tight">
              Global Recurring B2B Payments,
              <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                Simplified
              </span>
            </h2>
            <p className="text-responsive-base text-slate-600 max-w-4xl mx-auto leading-relaxed">
              Secure, scalable solutions for businesses of all sizes with enterprise-grade
              infrastructure and unmatched global reach.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
            {features.map((feature, index) => (
              <div
                key={index}
                className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 border border-slate-100 group hover:border-emerald-200 cursor-pointer"
              >
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-14 h-14 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                    <feature.icon className="w-7 h-7 text-white" />
                  </div>
                  <h3 className="text-responsive-base font-bold text-slate-900 group-hover:text-emerald-600 transition-colors duration-300">
                    {feature.title}
                  </h3>
                </div>
                <p className="text-slate-600 leading-relaxed group-hover:text-slate-700 transition-colors duration-300">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>

          <div className="text-center">
            <Button className="bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-500 hover:to-green-500 text-white rounded-full px-12 py-6 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-3 mx-auto group">
              <span>Request a Demo</span>
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};
