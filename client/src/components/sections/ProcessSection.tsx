import { CheckCircle, Play, Settings } from 'lucide-react';

export const ProcessSection = () => {
  const steps = [
    {
      number: 1,
      title: 'Setup & Approval',
      description:
        'Bank carries out KYC, due diligence and approves card limit. Use ERP or our interface. No code required.',
      features: ['KYC Verification', 'Instant Setup', 'Credit Assessment'],
      icon: CheckCircle,
    },
    {
      number: 2,
      title: 'Configuration',
      description:
        'Vendors are onboarded, payout types defined, caps configured. AI validates payees, documents and flow logic.',
      features: ['AI Validation', 'Smart Rules', 'Vendor Onboarding'],
      icon: Settings,
    },
    {
      number: 3,
      title: 'Execute & Monitor',
      description:
        'Authorized checkers release payouts. API triggers and card executes. Instant payout. Global FX support. Fully auditable.',
      features: ['Full Audit Trail', 'Global FX', 'Instant Execution'],
      icon: Play,
    },
  ];

  return (
    <section className="section-spacing bg-gradient-to-br from-orange-50/30 via-amber-50/20 to-yellow-50/30 relative overflow-hidden">
      {/* Minimal Background Pattern */}
      <div className="absolute inset-0 opacity-[0.015]">
        <div className="absolute top-20 right-20 w-96 h-96 bg-slate-200 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-72 h-72 bg-gray-200 rounded-full blur-3xl"></div>
      </div>

      {/* Synchronized with Hero section margins for perfect consistency */}
      <div className="max-w-none px-1 sm:px-2 lg:px-3 xl:px-4 w-full">
        <div className="w-full max-w-[98%] xl:max-w-[96%] mx-auto relative">
          {/* Header */}
          <div className="text-center mb-12 sm:mb-16 md:mb-20">
            <h2 className="text-responsive-xl font-bold text-slate-900 mb-4 sm:mb-6 tracking-tight">
              How{' '}
              <span className="bg-gradient-to-r from-green-600 via-emerald-500 to-teal-600 bg-clip-text text-transparent">
                RedGiraffe works
              </span>
            </h2>
            <p className="text-responsive-base text-slate-600 max-w-4xl mx-auto leading-relaxed">
              Simplify your payments in three seamless steps with enterprise-grade security and
              global infrastructure.
            </p>
          </div>

          {/* Process Timeline */}
          <div className="max-w-7xl mx-auto">
            {/* Desktop Timeline */}
            <div className="hidden lg:block relative mb-16">
              {/* Timeline Line */}
              <div className="absolute top-8 left-0 right-0 h-px bg-gradient-to-r from-transparent via-emerald-300 to-transparent"></div>

              {/* Timeline Dots */}
              <div className="flex justify-between items-center relative">
                {steps.map((step, index) => (
                  <div key={index} className="flex flex-col items-center">
                    <div className="w-6 h-6 bg-gradient-to-br from-emerald-500 to-green-600 rounded-full border-4 border-white shadow-lg relative z-10"></div>
                  </div>
                ))}
              </div>
            </div>

            {/* Steps Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {steps.map((step, index) => (
                <div
                  key={index}
                  className="bg-white rounded-2xl p-8 hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 border border-slate-100 hover:border-emerald-200 group cursor-pointer"
                >
                  {/* Step Icon and Title */}
                  <div className="mb-6">
                    <div className="w-14 h-14 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl flex items-center justify-center mb-4 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                      <step.icon className="w-7 h-7 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-slate-900 mb-4 group-hover:text-emerald-600 transition-colors duration-300">
                      {step.title}
                    </h3>
                    <p className="text-slate-600 leading-relaxed mb-6 group-hover:text-slate-700 transition-colors duration-300">
                      {step.description}
                    </p>
                  </div>

                  {/* Feature Tags */}
                  <div className="space-y-3">
                    {step.features.map((feature, featureIndex) => (
                      <div
                        key={featureIndex}
                        className="bg-slate-50 rounded-lg px-4 py-2 text-sm font-medium text-slate-700 border border-slate-200 hover:border-emerald-200 hover:bg-emerald-50 hover:text-emerald-700 transition-all duration-300 group-hover:bg-white group-hover:shadow-sm"
                      >
                        {feature}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
